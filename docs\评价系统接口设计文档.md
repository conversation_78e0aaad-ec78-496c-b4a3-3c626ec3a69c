# 评价系统接口设计文档

## 概述

本文档定义了宠物服务小程序评价系统的API接口规范，包括创建评价、获取评价、更新评价、删除评价等功能。

## 1. 创建评价接口

**接口地址**: `POST /customers/{customerId}/review`

**请求参数**:
```json
{
  "orderId": 123,
  "rating": 5,
  "comment": "服务非常好，护理师很专业，宠物很喜欢",
  "photoURLs": [
    "https://example.com/photo1.jpg",
    "https://example.com/photo2.jpg"
  ]
}
```

**参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | int | 是 | 订单ID |
| rating | tinyint | 是 | 评分，1-5星 |
| comment | text | 是 | 评价内容，最大500字符 |
| photoURLs | json | 否 | 图片URL数组，最多6张 |

**响应示例**:
```json
{
  "code": 200,
  "message": "评价创建成功",
  "data": {
    "id": 1,
    "orderId": 123,
    "rating": 5,
    "comment": "服务非常好，护理师很专业，宠物很喜欢",
    "photoURLs": [
      "https://example.com/photo1.jpg",
      "https://example.com/photo2.jpg"
    ],
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:30:00Z"
  }
}
```

## 2. 根据订单ID获取评价接口

**接口地址**: `GET /orders/{orderId}/review`

**请求参数**: 无

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "orderId": 123,
    "rating": 5,
    "comment": "服务非常好，护理师很专业，宠物很喜欢",
    "photoURLs": [
      "https://example.com/photo1.jpg",
      "https://example.com/photo2.jpg"
    ],
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:30:00Z"
  }
}
```

**无评价时响应**:
```json
{
  "code": 404,
  "message": "该订单暂无评价",
  "data": null
}
```

## 3. 获取客户评价列表接口

**接口地址**: `GET /customers/{customerId}/reviews`

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | int | 否 | 1 | 页码 |
| pageSize | int | 否 | 10 | 每页数量 |

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 25,
    "page": 1,
    "pageSize": 10,
    "totalPages": 3,
    "list": [
      {
        "id": 1,
        "orderId": 123,
        "rating": 5,
        "comment": "服务非常好，护理师很专业，宠物很喜欢",
        "photoURLs": [
          "https://example.com/photo1.jpg"
        ],
        "createdAt": "2024-01-15T10:30:00Z",
        "order": {
          "id": 123,
          "sn": "ORD20240115001",
          "service": {
            "id": 1,
            "serviceName": "宠物洗护",
            "logo": "https://example.com/service1.jpg"
          }
        }
      }
    ]
  }
}
```

## 4. 获取服务评价列表接口

**接口地址**: `GET /services/{serviceId}/reviews`

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | int | 否 | 1 | 页码 |
| pageSize | int | 否 | 10 | 每页数量 |

**响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 156,
    "page": 1,
    "pageSize": 10,
    "totalPages": 16,
    "averageRating": 4.8,
    "ratingDistribution": {
      "5": 120,
      "4": 25,
      "3": 8,
      "2": 2,
      "1": 1
    },
    "list": [
      {
        "id": 1,
        "orderId": 123,
        "rating": 5,
        "comment": "服务非常好，护理师很专业，宠物很喜欢",
        "photoURLs": [
          "https://example.com/photo1.jpg"
        ],
        "createdAt": "2024-01-15T10:30:00Z",
        "customer": {
          "id": 456,
          "nickname": "爱宠人士",
          "avatar": "https://example.com/avatar1.jpg"
        }
      }
    ]
  }
}
```

## 5. 更新评价接口

**接口地址**: `PUT /customers/{customerId}/review/{reviewId}`

**请求参数**:
```json
{
  "rating": 4,
  "comment": "修改后的评价内容",
  "photoURLs": [
    "https://example.com/new_photo1.jpg"
  ]
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "评价更新成功",
  "data": {
    "id": 1,
    "orderId": 123,
    "rating": 4,
    "comment": "修改后的评价内容",
    "photoURLs": [
      "https://example.com/new_photo1.jpg"
    ],
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T11:00:00Z"
  }
}
```

## 6. 删除评价接口

**接口地址**: `DELETE /customers/{customerId}/review/{reviewId}`

**请求参数**: 无

**响应示例**:
```json
{
  "code": 200,
  "message": "评价删除成功",
  "data": null
}
```



## 错误码定义

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 409 | 该订单已存在评价 |
| 422 | 参数验证失败 |
| 500 | 服务器内部错误 |

## 业务规则

1. **评价权限**: 只有订单状态为"已完成"的订单才能评价
2. **唯一性**: 每个订单只能评价一次
3. **参数限制**: 评价内容最多500字符，图片最多6张
4. **评分范围**: 评分必须在1-5之间

## 接口调用流程

### 评价创建流程
1. 用户在订单详情页点击"去评价"
2. 跳转到评价页面，加载订单信息
3. 用户选择星级评分（1-5星）
4. 用户输入评价内容（必填，最多500字符）
5. 用户可选择上传图片（最多6张）
6. 点击提交，调用创建评价接口
7. 提交成功后返回订单详情页

### 评价查看流程
1. 在订单详情页调用"根据订单ID获取评价"接口
2. 如果有评价则显示评价内容，隐藏"去评价"按钮
3. 在服务详情页调用"获取服务评价列表"接口展示所有评价
4. 在个人中心调用"获取客户评价列表"接口展示用户的所有评价

## 注意事项

1. 所有接口都需要进行用户身份验证
2. 图片URL使用现有的图片上传系统生成
3. 评价内容需要进行敏感词过滤
4. 评分必须为1-5的整数值
