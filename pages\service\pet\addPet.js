import user from '../../../api/modules/user';
import dictionaryAPI from '../../../api/modules/dictionary';
import {
  formatDateTime,
  generateId
} from '../../utils/util';

Page({
  data: {
    userInfo: null,
    globalData: {},
    currentDate: '',
    form: {
      id: undefined,
      name: '',
      avatar: '',
      type: 'cat',
      hairType: '',
      breed: '',
      gender: 1,
      birthday: '',
      weightType: '',
      weightTypeIndex: '',
      weightTypeDesc: '',
      isVaccine: true,
      isSterilization: true,
      isRepellent: true,
    },
    formData: {
      avatarListDatas: [],
      avatarDatas: [],
      petTypeDatas: [{
          value: 'cat',
          label: '猫猫',
          checked: true,
          url: 'https://xian7.zos.ctyun.cn/pet/static/cat.png',
          aurl: 'https://xian7.zos.ctyun.cn/pet/static/cat-a.png',
        },
        {
          value: 'dog',
          label: '狗狗',
          checked: false,
          url: 'https://xian7.zos.ctyun.cn/pet/static/dog.png',
          aurl: 'https://xian7.zos.ctyun.cn/pet/static/dog-a.png',
        },
        {
          value: 'qt',
          label: '其他',
          checked: false,
          url: 'https://xian7.zos.ctyun.cn/pet/static/qt.png',
          aurl: 'https://xian7.zos.ctyun.cn/pet/static/qt-a.png',
        },
      ],
      petgenderDatas: [{
          value: 1,
          label: '弟弟',
          checked: true,
          url: 'https://xian7.zos.ctyun.cn/pet/static/boy.png',
          aurl: 'https://xian7.zos.ctyun.cn/pet/static/boy-a.png',
        },
        {
          value: 2,
          label: '妹妹',
          checked: false,
          url: 'https://xian7.zos.ctyun.cn/pet/static/girl.png',
          aurl: 'https://xian7.zos.ctyun.cn/pet/static/girl-a.png',
        },
        {
          value: 0,
          label: '其他',
          checked: false,
          url: 'https://xian7.zos.ctyun.cn/pet/static/other.png',
          aurl: 'https://xian7.zos.ctyun.cn/pet/static/other-a.png',
        },
      ],
      weightTypes: [],
    },
  },

  async onLoad(option) {
    const weightTypes = await dictionaryAPI.list('宠物体型');
    this.setData({
      'formData.weightTypes': weightTypes,
    });
    const item = wx.getStorageSync('editPetItem');
    if (item) {
      this.setData({
          form: {
            ...item,
            birthday: formatDateTime(item.birthday),
          },
        },
        () => {
          // 更新宠物类别选中状态
          const petTypeDatas = this.data.formData.petTypeDatas.map((type) => ({
            ...type,
            checked: type.value === item.type,
          }));

          // 更新性别选中状态
          const petgenderDatas = this.data.formData.petgenderDatas.map(
            (gender) => ({
              ...gender,
              checked: gender.value === item.gender,
            })
          );

          // 获取过滤后的体型选项
          const filteredWeightTypes = this.getFilteredWeightTypes();

          // 如果有weightType，找到对应的索引和描述
          let weightTypeIndex = '';
          let weightTypeDesc = '';

          if (item.weightType && filteredWeightTypes.length > 0) {
            // 查找weightType在过滤后列表中的索引
            const index = filteredWeightTypes.findIndex(
              option => option.code === item.weightType
            );

            if (index !== -1) {
              weightTypeIndex = index;
              weightTypeDesc = filteredWeightTypes[index].description;
              console.log('编辑模式: 找到体型索引', index, '描述:', weightTypeDesc);
            }
          }

          this.setData({
            'formData.petTypeDatas': petTypeDatas,
            'formData.petgenderDatas': petgenderDatas,
            'formData.filteredWeightTypes': filteredWeightTypes,
            'form.weightTypeIndex': weightTypeIndex,
            'form.weightTypeDesc': weightTypeDesc,
          });
        }
      );
      wx.removeStorageSync('editPetItem');
    } else {
      // 初始化过滤后的体型选项（默认为猫）
      const filteredWeightTypes = this.getFilteredWeightTypes();
      this.setData({
        'formData.filteredWeightTypes': filteredWeightTypes,
      });
    }
    this.init();
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    this.setData({
      currentDate: `${year}-${month}-${day}`,
    });
  },

  onShow() {},

  async init() {
    await this.initResetform();
  },

  async testFunction(param) {
    let thiz = this.data;
    console.log(param);
  },

  async uploadFormAvatar(e) {
    const id = generateId()
    const res = await this.uploadImage(this, 'form.avatar', `pet/avatar/`, 1);
    console.log(res);
  },

  delFormAvatar(evt) {
    let {
      index
    } = evt.currentTarget.dataset;
    let avatarDatas = this.data.formData.avatarDatas;
    avatarDatas.splice(index, 1);
    let avatar = avatarDatas.join(',');
    this.setData({
      'form.avatar': avatar,
      'formData.avatarDatas': avatarDatas,
    });
  },

  previewFormAvatar(e) {
    wx.previewImage({
      current: e.currentTarget.dataset.url,
      urls: this.data.formData.avatarDatas,
    });
  },

  changeFormPettype(evt) {
    let pettype = evt.detail.value;
    let pettypeDatas = this.data.formData.petTypeDatas;
    let pettypeLabel = this.data.form.pettypeLabel;
    for (var i = 0, len = pettypeDatas.length; i < len; ++i) {
      pettypeDatas[i].checked = pettypeDatas[i].value == pettype;
      if (pettypeDatas[i].checked) {
        pettypeLabel = pettypeDatas[i].label;
      }
    }
    this.setData({
      'form.type': pettype,
      'form.pettype': pettype,
      'form.pettypeLabel': pettypeLabel,
      'formData.petTypeDatas': pettypeDatas,
      'form.hairType': '',
      'form.weightType': '', // 清空体型选择
      'form.weightTypeIndex': '', // 清空体型索引
      'form.weightTypeDesc': '' // 清空体型描述
    }, () => {
      // 更新过滤后的体型选项
      const filteredWeightTypes = this.getFilteredWeightTypes();
      this.setData({
        'formData.filteredWeightTypes': filteredWeightTypes
      });
    });
  },

  changeFormType(evt) {
    let typeIndex = evt.detail.value;
    let typeDatas = this.data.item.options;
    let typeLabel = typeDatas[typeIndex].content;
    let type = typeDatas[typeIndex].id;
    this.setData({
      'form.typeIndex': typeIndex,
      'form.typeLabel': typeLabel,
      'form.type': type,
    });
  },

  changeFormPetgender(evt) {
    let petgender = evt.detail.value;
    let petgenderDatas = this.data.formData.petgenderDatas;
    let petgenderLabel = this.data.form.petgenderLabel;
    for (var i = 0, len = petgenderDatas.length; i < len; ++i) {
      petgenderDatas[i].checked = petgenderDatas[i].value == petgender;
      if (petgenderDatas[i].checked) {
        petgenderLabel = petgenderDatas[i].label;
      }
    }
    this.setData({
      'form.gender': parseInt(petgender),
      'form.petgenderLabel': petgenderLabel,
      'formData.petgenderDatas': petgenderDatas,
    });
  },

  changeFormBirthday(evt) {
    const selectedDate = evt.detail.value;
    const today = new Date();
    const selected = new Date(selectedDate);

    // 计算31天前的日期
    const thirtyOneDaysAgo = new Date();
    thirtyOneDaysAgo.setDate(thirtyOneDaysAgo.getDate() - 31);

    if (selected > today) {
      wx.showToast({
        title: '宠物生日不能超过当天',
        icon: 'none',
      });
      return;
    }
    if (selected > thirtyOneDaysAgo) {
      wx.showToast({
        title: '只能选择31天前的日期',
        icon: 'none',
      });
      return;
    }
    this.setData({
      'form.birthday': selectedDate,
    });
  },

  changeFormHairType(e) {
    const value = e.currentTarget.dataset.value;
    this.setData({
      'form.hairType': value,
    });
  },

  changeFormIsSterilization() {
    this.setData({
      'form.isSterilization': !this.data.form.isSterilization,
    });
  },

  changeFormIsVaccine() {
    this.setData({
      'form.isVaccine': !this.data.form.isVaccine,
    });
  },

  changeFormIsRepellent() {
    this.setData({
      'form.isRepellent': !this.data.form.isRepellent,
    });
  },

  changeValue(e) {
    const value = e.detail.value;
    this.setData({
      'form.name': value,
    });
  },

  initResetform() {
    this.setData({
      initform: JSON.stringify(this.data.form),
    });
  },

  resetForm() {
    this.setData({
      form: JSON.stringify(this.data.initform),
    });
  },

  changeFormWeightType(evt) {
    const index = evt.detail.value;
    // 根据索引获取选中的选项
    const selectedOption = this.data.formData.filteredWeightTypes[index];

    if (selectedOption) {
      this.setData({
        'form.weightTypeIndex': index,
        'form.weightType': selectedOption.code,
        'form.weightTypeDesc': selectedOption.description
      });
      console.log('已选择体型:', selectedOption.description, '(', selectedOption.code, ')');
    }
  },

  // 根据宠物类型过滤体型选项
  getFilteredWeightTypes() {
    const petType = this.data.form.type;
    const allWeightTypes = this.data.formData.weightTypes || [];

    let prefix = '';
    if (petType === 'cat') {
      prefix = 'CAT_';
    } else if (petType === 'dog') {
      prefix = 'DOG_';
    } else {
      prefix = 'OTHER_';
    }

    return allWeightTypes.filter(option => option.code.startsWith(prefix));
  },
  showToast(title, icon = 'none') {
    wx.showToast({
      title: title,
      icon: icon,
      duration: 2000,
    });
  },

  async submitForm() {
    const form = this.data.form;
    if (!form.name) {
      this.showToast('宠物昵称不能为空');
      return;
    }
    if (!form.type) {
      this.showToast('宠物类别不能为空');
      return;
    }
    if (form.type === 'cat' && !form.hairType) {
      this.showToast('请选择宠物毛长');
      return;
    }
    if (!form.weightType) {
      this.showToast('请选择宠物体型');
      return;
    }
    // 创建表单数据的副本，避免直接修改原始数据
    const param = {...this.data.form};

    // 删除辅助字段，不发送到服务器
    delete param.weightTypeIndex;
    delete param.weightTypeDesc;
    delete param.pettypeLabel;
    delete param.petgenderLabel;

    if (!param.avatar) {
      delete param.avatar;
    }
    if (!param.breed) {
      delete param.breed;
    }
    if (!param.birthday) {
      delete param.birthday;
    }
    if (!param.id) {
      delete param.id;
    }
    wx.showLoading({
      title: '资料提交中...',
    });
    if (param.id) {
      const {
        id,
        ...rest
      } = param;
      await user
        .editPet(this.data.userInfo.id, id, rest)
        .then(() => {
          wx.showToast({
            title: '资料修改成功',
          });
          const curPet = wx.getStorageSync('selectPetInfo');
          if (curPet && curPet.id === param.id) {
            wx.setStorageSync('selectPetInfo', res);
          }
          setTimeout(() => {
            wx.navigateBack({
              delta: 1, // 返回上一页，相当于关闭当前页面
            });
          }, 500);
        })
        .catch(() => {
          wx.showToast({
            title: '资料修改失败',
            icon: 'error',
          });
        })
        .finally(() => {
          wx.hideLoading();
        });
    } else {
      await user
        .addPet(this.data.userInfo.id, param)
        .then(() => {
          wx.showToast({
            title: '资料提交成功',
          });
          setTimeout(() => {
            wx.navigateBack({
              delta: 1, // 返回上一页，相当于关闭当前页面
            });
          }, 500);
        })
        .catch(() => {
          wx.showToast({
            title: '资料提交失败',
            icon: 'error',
          });
        })
        .finally(() => {
          wx.hideLoading();
        });
    }
  },
});