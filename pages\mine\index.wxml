<view class="container containermine">
  <diy-navbar :isFixed="true" CustomBar='60' bgColor="white">
    <view slot="content">
      <view class="flex align-center flex-nowrap justify-center diygw-col-24">
        我的
      </view>
    </view>
  </diy-navbar>
  <view class="flex flex-wrap diygw-col-24 flex-direction-column flex12-clz">
    <view class="flex flex-wrap diygw-col-24 items-stretch flex10-clz">
      <view class="flex flex-wrap diygw-col-0 items-center">
        <image src="{{siteinfo.constant.defalutAvatar1}}" class="image7-size diygw-image diygw-col-0 image-round" mode="widthFix"></image>
        <!-- <image src="{{userInfo.avatar || siteinfo.constant.defalutAvatar1}}" class="image7-size diygw-image diygw-col-0 image-round" mode="widthFix" bind:tap="uploadAvatar"></image> -->
      </view>
      <view class="flex flex-wrap diygw-col-0 flex-direction-column justify-between flex20-clz">
        <view class="flex flex-wrap diygw-col-24 justify-between items-center flex13-clz">
          <text class="diygw-col-0 text12-clz"> {{userInfo.nickname||'未登录'}} </text>
        </view>
        <view class="flex flex-wrap diygw-col-24 items-center flex14-clz">
          <text class="diygw-col-0"> {{userInfo.phone||'待授权'}} </text>
        </view>
      </view>
      <view class="flex flex-wrap diygw-col-0 items-center">
        <text wx:if="{{userInfo}}" class="flex icon5 diygw-col-0 diy-icon-settings hidden"></text>
        <button wx:else data-type="login" bindtap="redirect" class="btn-login">去登录</button>
      </view>
    </view>
  </view>
  <view class="vipbg" data-type="vip" bind:tap="redirect">
    <image src='https://xian7.zos.ctyun.cn/pet/static/vipbg.png' class="diygw-image diygw-col-24 image-vipbg" mode="widthFix" />
    <view class='vip-contain'>
      <view class="viptext">购买贝宠权益卡，立享更多优惠！</view>
      <view class='btn'>
        去购买
        <image src='https://xian7.zos.ctyun.cn/pet/static/arrow.png' class="diygw-image image-govip" mode="widthFix" />
      </view>
    </view>
  </view>
  <view class="flex flex-wrap diygw-col-24 flex-direction-column items-center flex16-clz">
    <view class="flex flex-wrap diygw-col-24 justify-between flex17-clz">
      <view class="flex flex-wrap diygw-col-0 justify-center items-center">
        <text class="diygw-text-line1 diygw-col-0 text13-clz"> 我的爱宠 </text>
      </view>
      <view class="flex flex-wrap diygw-col-0 items-center flex30-clz" data-type="pet" bind:tap="redirect">
        <text class="diygw-col-0 text32-clz"> 宠物管理 </text>
        <text class="flex icon6 diygw-col-0 diy-icon-right"></text>
      </view>
    </view>
    <scroll-view wx:if="{{pets.length}}" scroll-x class="flex scroll-view flex-wrap diygw-col-24 flex6-clz">
      <view class="flex flex-nowrap">
        <view wx:for="{{pets}}" wx:for-item="item" wx:for-index="index" wx:key="index" class="flex diygw-col-18 items-stretch flex-nowrap flex46-clz" style="background-color: {{item.gender===1 ? 'rgba(122, 221, 252, 0.3)':item.gender===0 ?'rgba(255, 251, 220, 1)':''}};">
          <view class="flex flex-wrap diygw-col-0 flex-direction-column justify-center items-center">
            <image src="{{item.avatar||siteinfo.constant.pet[item.type]}}" class="image7-size diygw-image diygw-col-0 image7-clz" mode="widthFix"></image>
          </view>
          <view class="flex flex-wrap diygw-col-0 flex-direction-column justify-between flex48-clz">
            <text class="diygw-col-0 text23-clz"> {{item.name}} </text>
            <view class="flex flex-wrap diygw-col-24 flex49-clz">
              <text class="diygw-text-line1 diygw-col-0 text24-pet-ly"> {{item.formattedBri}} </text>
              <text class="diygw-text-line1 diygw-col-0 text24-pet-ly"> {{item.gender===0 ? '未知':item.gender===1?'弟弟':'妹妹'}} </text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
  <view class="flex flex-wrap diygw-col-24 flex-direction-column flex38-clz">
    <view class="flex flex-wrap diygw-col-24 justify-between items-center flex21-clz">
      <text class="diygw-text-line1 diygw-col-0 text-clz"> 服务订单 </text>
      <view class="flex flex-wrap diygw-col-0 items-center flex17-clz" data-type="serviceAll" bind:tap="redirect">
        <text class="diygw-text-line1 diygw-col-0"> 全部 </text>
        <text class="flex icon6 diygw-col-0 diy-icon-right"></text>
      </view>
    </view>
    <view class="flex flex-wrap diygw-col-24">
      <view class="flex flex-wrap diygw-col-6 flex-direction-column items-center flex18-clz" data-type="待付款" bind:tap="redirect">
        <image src="//xian7.zos.ctyun.cn/pet/static/fukuan.png" class="image-size diygw-image diygw-col-0 image-clz" mode="widthFix"></image>
        <text class="diygw-text-line1 diygw-col-0 text11-clz"> 待付款 </text>
      </view>
      <view class="flex flex-wrap diygw-col-6 flex-direction-column items-center flex18-clz" data-type="待接单" bind:tap="redirect">
        <image src="//xian7.zos.ctyun.cn/pet/static/daijie.png" class="image-size diygw-image diygw-col-0 image-clz" mode="widthFix"></image>
        <text class="diygw-text-line1 diygw-col-0 text11-clz"> 待接单 </text>
      </view>
      <view class="flex flex-wrap diygw-col-6 flex-direction-column items-center flex18-clz" data-type="待服务,已出发,服务中,退款中" bind:tap="redirect">
        <image src="//xian7.zos.ctyun.cn/pet/static/<EMAIL>" class="image-size diygw-image diygw-col-0 image-clz" mode="widthFix"></image>
        <text class="diygw-text-line1 diygw-col-0 text11-clz"> 进行中 </text>
      </view>
      <view class="flex flex-wrap diygw-col-6 flex-direction-column items-center flex18-clz" data-type="已完成,已退款" bind:tap="redirect">
        <image src="//xian7.zos.ctyun.cn/pet/static/<EMAIL>" class="image-size diygw-image diygw-col-0 image-clz" mode="widthFix"></image>
        <text class="diygw-text-line1 diygw-col-0 text11-clz"> 已完成 </text>
      </view>
    </view>
  </view>
  <view class="flex flex-wrap diygw-col-24 flex-direction-column flex38-clz">
    <view class="flex flex-wrap diygw-col-24">
      <view class="flex flex-wrap diygw-col-6 flex-direction-column items-center flex18-clz" data-type="adress" bind:tap="redirect">
        <image src="//xian7.zos.ctyun.cn/pet/static/dizhi.png" class="image-size diygw-image diygw-col-0 image-sm" mode="widthFix"></image>
        <text class="diygw-text-line1 diygw-col-0 text11-clz"> 地址管理 </text>
      </view>
      <view class="flex flex-wrap diygw-col-6 flex-direction-column items-center flex18-clz" data-type="ticket" bind:tap="redirect">
        <image src="//xian7.zos.ctyun.cn/pet/static/kaquan.png" class="image-size diygw-image diygw-col-0 image-sm" mode="widthFix"></image>
        <text class="diygw-text-line1 diygw-col-0 text11-clz"> 我的卡券 </text>
      </view>
      <button open-type="contact" session-from="客服入口" class="flex flex-wrap diygw-col-6 flex-direction-column items-center flex18-clz card">
        <image src="//xian7.zos.ctyun.cn/pet/static/kefu.png" class="image-size diygw-image diygw-col-0 image-sm" mode="widthFix"></image>
        <text class="diygw-text-line1 diygw-col-0 text11-clz"> 在线客服 </text>
      </button>
      <view class="flex flex-wrap diygw-col-6 flex-direction-column items-center flex18-clz" data-type="suggestion" bind:tap="redirect">
        <image src="//xian7.zos.ctyun.cn/pet/static/fankui.png" class="image-size diygw-image diygw-col-0 image-sm" mode="widthFix"></image>
        <text class="diygw-text-line1 diygw-col-0 text11-clz"> 建议反馈 </text>
      </view>
      <!-- </view>
    <view class="flex flex-wrap diygw-col-24"> -->
      <view class="flex flex-wrap diygw-col-6 flex-direction-column items-center flex18-clz" data-type="clearstorge" bind:tap="redirect">
        <image src="//xian7.zos.ctyun.cn/pet/static/huancun.png" class="image-size diygw-image diygw-col-0 image-sm" mode="widthFix"></image>
        <text class="diygw-text-line1 diygw-col-0 text11-clz"> 清除缓存 </text>
      </view>
      <view class="flex flex-wrap diygw-col-6 flex-direction-column items-center flex18-clz" data-type="user" bind:tap="redirect">
        <image src="//xian7.zos.ctyun.cn/pet/static/yonghux.png" class="image-size diygw-image diygw-col-0 image-sm" mode="widthFix"></image>
        <text class="diygw-text-line1 diygw-col-0 text11-clz"> 用户协议 </text>
      </view>
      <view class="flex flex-wrap diygw-col-6 flex-direction-column items-center flex18-clz" data-type="previate" bind:tap="redirect">
        <image src="//xian7.zos.ctyun.cn/pet/static/yinsi.png" class="image-size diygw-image diygw-col-0 image-sm" mode="widthFix"></image>
        <text class="diygw-text-line1 diygw-col-0 text11-clz"> 隐私协议 </text>
      </view>
    </view>
  </view>
  <custom-modal show="{{showModal}}" bind:confirm="onModalConfirm" title="{{modalTitle}}" content="{{modalContent}}"></custom-modal>
  <custom-tabbar currentActive='mine' />
</view>