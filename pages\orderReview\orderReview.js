// pages/orderReview/orderReview.js
import orderApi from '../../api/modules/order';
import reviewApi from '../../api/modules/review';

Page({
  data: {
    orderId: '',
    orderDetail: null,
    rating: 0,
    comment: '',
    photoList: [],
    isSubmitting: false,
    canSubmit: false,
    placeholder: '服务感受如何？来分享您的体验吧',
    ratingTexts: ['很差', '较差', '一般', '满意', '非常满意'],
    userInfo: null,
  },

  onLoad(options) {
    const { orderId } = options;
    if (orderId) {
      this.setData({ orderId });
      this.loadOrderDetail();
    }

    // 获取用户信息
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      this.setData({ userInfo });
    }
  },

  /**
   * 加载订单详情
   */
  async loadOrderDetail() {
    try {
      wx.showLoading({ title: '加载中...' });
      const { userInfo, orderId } = this.data;

      if (!userInfo) {
        wx.showToast({
          title: '请先登录',
          icon: 'none',
        });
        return;
      }

      const orderDetail = await orderApi.getDetail(userInfo.id, orderId);
      this.setData({ orderDetail });
    } catch (error) {
      console.error('加载订单详情失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none',
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 设置评分
   */
  setRating(e) {
    const rating = e.currentTarget.dataset.rating;
    this.setData({ rating }, () => {
      this.checkCanSubmit();
    });
  },

  /**
   * 评价内容输入
   */
  onCommentInput(e) {
    this.setData(
      {
        comment: e.detail.value,
      },
      () => {
        this.checkCanSubmit();
      }
    );
  },

  /**
   * 选择图片
   */
  chooseImage() {
    const { photoList, orderId } = this.data;
    const remainCount = 6 - photoList.length;
    // 使用现有的上传功能
    this.uploadImage(
      this,
      'photoList', // 存储字段
      `review/${orderId}/`, // 上传key前缀
      remainCount // 最大数量
    );
  },

  /**
   * 删除图片
   */
  deletePhoto(e) {
    const index = e.currentTarget.dataset.index;
    const photoList = [...this.data.photoList];
    photoList.splice(index, 1);
    this.setData({ photoList });
  },

  /**
   * 预览图片
   */
  previewImage(e) {
    const url = e.currentTarget.dataset.url;
    wx.previewImage({
      current: url,
      urls: this.data.photoList,
    });
  },

  /**
   * 检查是否可以提交
   */
  checkCanSubmit() {
    const { rating, comment } = this.data;
    const canSubmit = rating > 0 && comment.trim().length > 0;
    this.setData({ canSubmit });
    return canSubmit;
  },

  /**
   * 提交评价
   */
  async submitReview() {
    const { rating, comment, photoList, orderId, userInfo, canSubmit } = this.data;

    if (!canSubmit) {
      wx.showToast({
        title: '请完成评分和评价',
        icon: 'none',
      });
      return;
    }

    this.setData({ isSubmitting: true });

    try {
      const reviewData = {
        orderId: parseInt(orderId),
        rating: rating,
        comment: comment.trim(),
        photoURLs: photoList,
      };

      await reviewApi.create(userInfo.id, reviewData);

      wx.showToast({
        title: '评价成功',
        icon: 'success',
      });

      // 延迟返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    } catch (error) {
      console.error('提交评价失败:', error);
      wx.showToast({
        title: '提交失败，请重试',
        icon: 'none',
      });
    } finally {
      this.setData({ isSubmitting: false });
    }
  },
});
