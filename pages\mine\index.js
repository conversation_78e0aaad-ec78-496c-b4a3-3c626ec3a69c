// pages/mine/index.js
import siteinfo from '../../siteinfo.js';
import userApi from '../../api/modules/user';
import utils from '../utils/util';
import { OrderStatus } from '../../common/constant.js';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    siteinfo,
    userInfo: null,
    pets: [],
    showModal: false,
    modalTitle: '敬请期待',
    modalContent: '该功能正在开发中，我们正努力为您带来更好的体验！',
  },
  onShow() {
    wx.hideLoading();
    this.getPets();
  },

  onShareAppMessage: function () {
    return {
      title: '贝宠约洗，足不出户享精致洗护～',
      path: `/pages/index/index?aUserId=${this.data?.userInfo?.id || ''}&shareCode=${
        this.data?.userInfo?.promotionCode || ''
      }&shareTime=${Date.now()}`,
      imageUrl: 'https://xian7.zos.ctyun.cn/pet/static/share.png',
      // imageUrl: '/assets/share.png' // 这里填写图片的相对路径或绝对路径
    };
  },
  onShareTimeline: function () {
    return {
      title: '「忙到没空送洗？上门服务解忧！」贝宠约洗，专业到家超省心～',
      imageUrl: 'https://xian7.zos.ctyun.cn/pet/static/share1.png',
      query: `aUserId=${this.data?.userInfo?.id || ''}&shareCode=${
        this.data?.userInfo?.promotionCode || ''
      }&shareTime=${Date.now()}`,
      // imageUrl: '/assets/share.png' // 这里填写图片的相对路径或绝对路径
    };
  },

  uploadAvatar() {
    this.uploadImage(
      this,
      'userInfo.avatar', // 想把url存入哪个字段
      `avatar/`, // 上传的key前缀,例如avatar/
      1
    );
  },
  redirect(evt) {
    let { type } = evt.currentTarget.dataset;
    let url;
    const thiz = this;
    const userInfo = thiz.data.userInfo;
    if (!userInfo) {
      wx.navigateTo({
        url: '/pages/login/index',
      });
      return;
    }
    if (type === 'customer') {
      this.setData({
        showModal: true,
        modalTitle: '在线客服',
      });
      return;
    }
    if (type === 'clearstorge') {
      wx.showModal({
        title: '清理缓存',
        content: '您的登录信息也将会同步清除，确定要清理吗？',
        success: res => {
          if (res.confirm) {
            wx.clearStorage();
            wx.clearStorageSync();
            wx.showToast({
              title: '缓存清理完成',
              duration: 3000,
            }).then(() => {
              setTimeout(() => {
                wx.redirectTo({
                  url: '/pages/index/index',
                });
              }, 1000);
            });
          }
        },
      });
      return;
    }
    switch (type) {
      case 'serviceAll':
        url = '/pages/serviceOrder/index';
        break;
      case OrderStatus.待付款:
      case OrderStatus.待接单:
      case [OrderStatus.待服务, OrderStatus.已出发, OrderStatus.服务中, OrderStatus.退款中].join(','):
      case [OrderStatus.已完成, OrderStatus.已退款].join(','):
        url = '/pages/serviceOrder/index?type=' + type;
        break;
      case 'adress':
        url = '/pages/service/adress/index';
        break;
      case 'user':
        url = '/pages/mine/userAgreement/index';
        break;
      case 'previate':
        url = '/pages/mine/privacyAgreement/index';
        break;
      case 'pet':
        url = '/pages/service/pet/index';
        break;
      case 'vip':
        url = '/pages/mine/rightsCenter/rightsCenter';
        break;
      case 'ticket':
        url = '/pages/mine/myTickets/myTickets';
        break;
      case 'login':
        url = '/pages/login/index';
        break;
      default:
        break;
    }
    if (url) {
      thiz.navigateTo({
        type: 'page',
        url,
        curTab: type === 'serviceAll' ? 'all' : type,
      });
    } else {
      // 显示自定义模态框
      this.setData({
        showModal: true,
        modalTitle: '敬请期待',
        modalContent: '该功能正在开发中，我们正努力为您带来更好的体验！',
      });
    }
  },
  getPets() {
    const userInfo = this.data.userInfo;
    if (!userInfo) {
      this.setData({
        pets: [],
      });
      return;
    }
    userApi
      .getPets(userInfo.id)
      .then(data => {
        const formattedData = data.map(item => {
          if (typeof item.bri === 'number' && !isNaN(item.bri)) {
            item.formattedBri = utils.formatAge(item.bri);
          } else {
            item.formattedBri = '无效年龄';
          }
          return item;
        });
        this.setData({
          pets: formattedData,
        });
      })
      .catch(err => {
        console.log(err);
      });
  },
  // 模态框确认事件
  onModalConfirm() {
    this.setData({
      showModal: false,
    });
  },
});
